module(..., package.seeall)

require 'sys'
require 'log'
require 'misc'
require 'mqtt'
require 'socket'
local my_utils = require('my_utils') -- Require the my_utils module

-- Expose the servers table
servers = {
    server1 = {
        ip = "bot.elec.mn",
        port = 1883
    },
    server2 = {
        ip = "app.elec.mn",
        port = 1883
    },
    server3 = {
        ip = "elec.mn",
        port = 1883
    }
}

local mqtt_server, mqtt_port = servers.server3.ip, servers.server3.port -- Default to server3
local mqtt_clientid, mqtt_username, mqtt_password = nil, "admin", "public"

local mqtt_ready = false
local retryConnectCnt = 0
local msgQueue = {}
local mqttPayload = nil

-- Keep-Alive interval in seconds (handled internally by the library)
local KEEP_ALIVE_INTERVAL = 60

-- Watchdog timeout in seconds (e.g., 24 hours)
local WATCHDOG_TIMEOUT = 60 * 60
sys.subscribe("IMEI_READY_IND", function()
    mqtt_clientid = misc.getImei()
    log.info("MQTT", "Imei:", mqtt_clientid)
end)

-- Function to save the selected server to a file
local function saveServerSelection(ip, port)
    local success, err = pcall(my_utils.writeToFile, "/user_dir/mqtt_server.txt", ip .. ":" .. port)
    if success then
        log.info("MQTT", "Server selection saved: ", ip, ":", port)
    else
        log.error("MQTT", "Failed to save server selection: ", err)
    end
end

-- Function to load the server selection from a file
local function loadServerSelection()
    if my_utils.fileExists("/user_dir/mqtt_server.txt") then
        local content = my_utils.readFile("/user_dir/mqtt_server.txt")
        local domain, port = string.match(content, "([^:]+):(%d+)")
        port = tonumber(port)
        if domain and port and port > 0 and port < 65536 then
            mqtt_server, mqtt_port = domain, port
            log.info("MQTT", "Loaded server selection: " .. domain .. ":" .. port)
        else
            log.warn("MQTT", "Server selection file contains invalid domain or port. Using default server3.")
        end
    else
        log.info("MQTT", "No server selection file found. Using default server3.")
    end
end

-- Call loadServerSelection at startup
loadServerSelection()

-- Log the selected server at startup
log.info("MQTT", "=== MQTT Server Configuration ===")
log.info("MQTT", "Selected server: " .. mqtt_server .. ":" .. mqtt_port)
log.info("MQTT", "Available servers:")
for key, server in pairs(servers) do
    log.info("MQTT", "  " .. key .. ": " .. server.ip .. ":" .. server.port)
end
log.info("MQTT", "===============================")

-- Function to set the MQTT server dynamically
function setMqttServer(serverKey, ip, port)
    if serverKey and servers[serverKey] then
        local server = servers[serverKey]
        if mqtt_server ~= server.ip or mqtt_port ~= server.port then
            mqtt_server, mqtt_port = server.ip, server.port
            saveServerSelection(server.ip, server.port)
            mqtt_ready = false
            log.info("MQTT", "Server changed to: ", mqtt_server, ":", mqtt_port)
            
            -- Schedule a restart to apply changes
            sys.taskInit(function()
                -- Signal the mqtt_run task to stop
                sys.publish("MQTT_RECONNECT")
                
                -- Wait before restarting
                log.info("MQTT", "Scheduling restart in 3 seconds to apply server change...")
                sys.wait(3000)
                
                -- Restart the system
                local r = 1
                local success, err = pcall(function() sys.restart(r) end)
                if not success then
                    log.error("MQTT", "Failed to restart: " .. (err or "Unknown error"))
                end
            end)
            
            return true, "Server changed to " .. serverKey .. ". Restarting in 3 seconds..."
        else
            log.info("MQTT", "Server is already set to: ", mqtt_server)
            return true, "Server is already set to " .. serverKey
        end
    elseif ip and port then
        mqtt_server, mqtt_port = ip, port
        saveServerSelection(ip, port)
        mqtt_ready = false
        log.info("MQTT", "Custom server set to: ", mqtt_server, ":", mqtt_port)
        
        -- Schedule a restart to apply changes
        sys.taskInit(function()
            -- Signal the mqtt_run task to stop
            sys.publish("MQTT_RECONNECT")
            
            -- Wait before restarting
            log.info("MQTT", "Scheduling restart in 3 seconds to apply server change...")
            sys.wait(3000)
            
            -- Restart the system
            local r = 1
            local success, err = pcall(function() sys.restart(r) end)
            if not success then
                log.error("MQTT", "Failed to restart: " .. (err or "Unknown error"))
            end
        end)
        
        return true, "Custom server set to " .. ip .. ":" .. port .. ". Restarting in 3 seconds..."
    else
        log.error("MQTT", "Invalid server key or custom IP/port")
        return false, "Invalid server key or custom IP/port"
    end
end

function disconnect()
    if mqttClient then
        log.info("MQTT", "Disconnecting from current server")
        mqttClient:disconnect()
        mqtt_ready = false
        return true
    end
    return false
end

function read_msg()
    -- log.info("read_msg", "Reading MQTT payload: ", mqttPayload)
    return mqttPayload
end

function clear_msg()
    log.info("clear_msg", "Clearing MQTT payload")
    mqttPayload = nil
end

function isReady()
    return mqtt_ready
end

function clientID()
    return mqtt_clientid
end

function insertMsg(topic, payload, qos)
    log.info("insertMsg", "Inserting message to queue: ", topic, payload, qos)
    local success, err = pcall(function()
        table.insert(msgQueue, {
            t = topic,
            p = payload,
            q = qos
        })
        sys.publish("APP_SOCKET_SEND_DATA")
    end)
    if success then
        return true
    else
        log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
        return false
    end
end

function mqttOutMsg_proc(mqttClient)
    while #msgQueue > 0 do
        local outMsg = table.remove(msgQueue, 1)
        log.info("mqttOutMsg_proc", "Publishing message: ", outMsg.t, outMsg.p, outMsg.q)
        local result = mqttClient:publish(outMsg.t, outMsg.p, outMsg.q)
        if not result then
            log.error("mqttOutMsg_proc", "Failed to publish message")
            return
        end
    end
    return true
end

function mqttInMsg_proc(mqttClient)
    local result, data
    while true do
        result, data = mqttClient:receive(1000, "APP_SOCKET_SEND_DATA") -- Reduced timeout
        if result then
            if data.id == PINGRESP then
                log.info("Keep-Alive", "Received PINGRESP from server")
            elseif data.topic and data.payload then
                log.info("mqttInMsg_proc", "Received data: ", data.topic, string.toHex(data.payload))
                mqttPayload = data.payload
                sys.publish("MQTT_MESSAGE_RECEIVED")
                log.debug("Watchdog", "lastMsgTime set to: " .. os.time())
            else
                log.warn("mqttInMsg_proc", "Received unknown data type")
            end
        elseif data == "timeout" then
            log.debug("mqttInMsg_proc", "Receive timeout, continuing...")
        else
            log.warn("mqttInMsg_proc", "Failed to receive data: ", tostring(data))
            break
        end
    end
    return result or data == "timeout" or data == "APP_SOCKET_SEND_DATA"
end

function mqtt_run()
    retryConnectCnt = 0
    local serverKeys = {"server1", "server2", "server3"}
    local currentServerIndex = 3 -- Start with server3 as default

    while true do
        mqtt_ready = false
        while not socket.isReady() do
            sys.wait(500)
        end
        while mqtt_clientid == nil do
            sys.wait(500)
        end

        log.info("MQTT", "=== Connecting to MQTT Server ===")
        log.info("MQTT", "Server: " .. mqtt_server)
        log.info("MQTT", "Port: " .. mqtt_port)
        log.info("MQTT", "Client ID: " .. mqtt_clientid)
        log.info("MQTT", "================================")
        
        mqttClient = mqtt.client(mqtt_clientid, KEEP_ALIVE_INTERVAL, mqtt_username, mqtt_password)
        if mqttClient:connect(mqtt_server, mqtt_port, "tcp") then
            if mqttClient:subscribe(mqtt_clientid, 0) then
                mqtt_ready = true
                log.info("MQTT", "=== MQTT Connection Successful ===")
                log.info("MQTT", "Connected to: " .. mqtt_server .. ":" .. mqtt_port)
                log.info("MQTT", "Subscribed to topic: " .. mqtt_clientid)
                log.info("MQTT", "==================================")
                retryConnectCnt = 0 -- Reset on successful connection
                
                -- Main connection loop
                while true do
                    local evt = sys.waitUntil("MQTT_RECONNECT", 1000)
                    if evt then
                        log.info("MQTT", "Reconnection signal received, disconnecting...")
                        mqttClient:disconnect()
                        break
                    end
                    if not mqttInMsg_proc(mqttClient) then
                        log.error("mqttTask", "MQTT In Message Processing Error")
                        break
                    end
                    if not mqttOutMsg_proc(mqttClient) then
                        log.error("mqttTask", "MQTT Out Message Processing Error")
                        break
                    end
                    sys.wait(100)
                end
            end
            mqtt_ready = false
        else
            retryConnectCnt = retryConnectCnt + 1
            log.error("MQTT", "Failed to connect to server: ", mqtt_server, " Retry count: ", retryConnectCnt)
        end
        
        mqttClient:disconnect()
        if retryConnectCnt >= 5 then
            log.error("mqtt_run", "=== MQTT Connection Failed 5 times, switching server ===")
            currentServerIndex = (currentServerIndex % #serverKeys) + 1
            local newServerKey = serverKeys[currentServerIndex]
            mqtt_server, mqtt_port = servers[newServerKey].ip, servers[newServerKey].port
            log.info("mqtt_run", "Switching to server: " .. newServerKey)
            log.info("mqtt_run", "New server: " .. mqtt_server .. ":" .. mqtt_port)
            log.info("mqtt_run", "=================================================")
            retryConnectCnt = 0 -- Reset retry count after switching server
        end
        local waitTime = math.min(2 ^ retryConnectCnt, 60) -- Exponential backoff up to 60 seconds
        log.info("mqtt_run", "Waiting " .. waitTime .. " seconds before retrying...")
        sys.wait(waitTime * 1000)
    end
end

-- Initialize the MQTT task
sys.taskInit(mqtt_run)

-- Initialize the adjusted watchdog task
local lastMsgTime = os.time()

sys.subscribe("MQTT_MESSAGE_RECEIVED", function()
    lastMsgTime = os.time()
    log.info("Watchdog", "lastMsgTime updated to: " .. lastMsgTime)

end)

function watchdog()
    while true do
        sys.wait(60000) -- Check every minute
        if os.difftime(os.time(), lastMsgTime) > WATCHDOG_TIMEOUT then
            log.warn("Watchdog", "No messages received for " .. (WATCHDOG_TIMEOUT / 3600) .. " hours. Reconnecting...")
            sys.publish("MQTT_RECONNECT")
        else
            log.info("Watchdog",
                "Connection is healthy. Last message received " .. os.difftime(os.time(), lastMsgTime) ..
                    " seconds ago.")
        end
    end
end

sys.taskInit(watchdog)

-- Function to update a server entry in the servers table
function updateServerEntry(serverKey, domain, port)
    if not servers[serverKey] then
        return false, "Invalid server key. Use server1, server2, or server3."
    end
    
    if not domain or domain == "" then
        return false, "Invalid domain name."
    end
    
    port = tonumber(port) or 1883 -- Default to 1883 if port is not specified
    
    if port <= 0 or port > 65535 then
        return false, "Invalid port number. Must be between 1-65535."
    end
    
    -- Update the server entry
    servers[serverKey].ip = domain
    servers[serverKey].port = port
    
    -- Save the updated server configuration
    local success, err = pcall(my_utils.writeToFile, "/user_dir/" .. serverKey .. "_config.txt", domain .. ":" .. port)
    if not success then
        log.error("MQTT", "Failed to save server configuration: " .. (err or "Unknown error"))
        return false, "Failed to save server configuration."
    end
    
    log.info("MQTT", "Updated " .. serverKey .. " to " .. domain .. ":" .. port)
    return true, "Updated " .. serverKey .. " to " .. domain .. ":" .. port
end

-- Function to load server configurations at startup
local function loadServerConfigurations()
    for serverKey, _ in pairs(servers) do
        local configFile = "/user_dir/" .. serverKey .. "_config.txt"
        if my_utils.fileExists(configFile) then
            local content = my_utils.readFile(configFile)
            local domain, port = string.match(content, "([^:]+):(%d+)")
            port = tonumber(port)
            if domain and port and port > 0 and port < 65536 then
                servers[serverKey].ip = domain
                servers[serverKey].port = port
                log.info("MQTT", "Loaded " .. serverKey .. " configuration: " .. domain .. ":" .. port)
            else
                log.warn("MQTT", "Invalid " .. serverKey .. " configuration in file. Using default.")
            end
        end
    end
end

-- Call loadServerConfigurations at startup
loadServerConfigurations()

return usermqtt
