local commands = {}
local vars = require("variables")
local utils = require('my_utils')
local PinModule = require('PinModule') -- Import PinModule
local usermqtt = require 'usermqtt'
local sms = require("sms") -- Import sms module
local SensorModule = require("SensorModule") -- Import SensorModule for voltage reading
require 'sys'

-- A utility function to safely execute a function that does not yield
local function safeExecute(func, ...)
    local success, err = pcall(func, ...)
    if not success then
        log.error("commands", "Error during execution: " .. (err or "Unknown error"))
    end
    return success
end

local function MP3_Play(filePath)
    if vars.sound_flag then
        log.info("MP3 Play", filePath) -- Logging for debugging
        audio.play(4, "FILE", filePath, 7) -- Play the audio file
    end
end

function commands.checkCommand()
    log.info("commands", "Calling utils.sendData with 'check'")
    if vars.sound_flag then
        MP3_Play("/lua/check.mp3") -- Play check sound
    end
end

function commands.lockCommand()
    -- Check if the device is licensed
    if not vars.isLicensed then
        log.warn("License", "Device not licensed. Cannot execute lockCommand")
        MP3_Play("/lua/license.mp3") -- Play error sound
        -- Send MQTT notification about license issue
        sys.taskInit(function()
            local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"license_required"}', 0)
            if not success then
                log.error("usermqtt", "Failed to send license notification: " .. (err or "Unknown error"))
            end
        end)

    end

    local success = pcall(pmd.ldoset, 7, 5) -- Direct pcall to avoid yield issues
    if not success then
        log.error("commands", "Error in pmd.ldoset during lockCommand")
        return
    end
    success = pcall(PinModule.relayControl, "Go3", 1) -- Direct pcall
    if not success then
        log.error("commands", "Error in PinModule.relayControl during Go3")
        return
    end

    sys.taskInit(function()

        log.info("locked")
        MP3_Play("/lua/lock.mp3")
        sys.wait(vars.lock_wait_duration)
        PinModule.relayControl("Key1", 1) -- This may yield, so keep inside coroutine
        sys.wait(vars.lock_press_duration)
        PinModule.relayControl("Key1", 0) -- This may yield, so keep inside coroutine
        if vars.key_state == false then
            pmd.ldoset(0, 5) -- Direct call since this may yield
        end

        success = pcall(PinModule.relayControl, "Go3", 0) -- Direct pcall
        if not success then
            log.error("commands", "Error in PinModule.relayControl during Go3")
            return
        end
    end)
end

function commands.unlockCommand()
    -- Check if the device is licensed
    if not vars.isLicensed then
        log.warn("License", "Device not licensed. Cannot execute unlockCommand")
        MP3_Play("/lua/license.mp3") -- Play error sound
        -- Send MQTT notification about license issue
        sys.taskInit(function()
            local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"license_required"}', 0)
            if not success then
                log.error("usermqtt", "Failed to send license notification: " .. (err or "Unknown error"))
            end
        end)
        return
    end

    local success = pcall(pmd.ldoset, 7, 5) -- Direct pcall
    if not success then
        log.error("commands", "Error in pmd.ldoset during unlockCommand")
        return
    end
    success = pcall(PinModule.relayControl, "Go3", 1) -- Direct pcall
    if not success then
        log.error("commands", "Error in PinModule.relayControl during Go3")
        return
    end


    sys.taskInit(function()
          log.info("unlocked")
        MP3_Play("/lua/lock.mp3")
        sys.wait(vars.unlock_wait_duration)
        PinModule.relayControl("Key2", 1) -- This may yield, so keep inside coroutine
        sys.wait(vars.unlock_press_duration)
        PinModule.relayControl("Key2", 0) -- This may yield, so keep inside coroutine
        if vars.key_state == false then
            pmd.ldoset(0, 5) -- Direct call since this may yield
        end
        success = pcall(PinModule.relayControl, "Go3", 0) -- Direct pcall
        if not success then
            log.error("commands", "Error in PinModule.relayControl during Go2")
            return
        end

    end)
end

local last_mirror_action = "none" -- Track the last mirror action for Geely Atlas mode
local GEELY_UNLOCK_DURATION = 8000 -- Default 5 seconds for unlock in Geely mode
local GEELY_LOCK_DURATION = 2000 -- Default 2 seconds for lock in Geely mode

function commands.mirrorCommand()
    -- Initialize key for immobilizer recognition
    local success = pcall(pmd.ldoset, 7, 5) -- Direct pcall to avoid yield issues
    if not success then
        log.error("commands", "Error in pmd.ldoset during mirrorCommand")
        return
    end
    success = pcall(PinModule.relayControl, "Go3", 1) -- Direct pcall
    if not success then
        log.error("commands", "Error in PinModule.relayControl during Go3")
        return
    end

    MP3_Play("/lua/check.mp3")
    sys.taskInit(function()
        -- Special case for Geely Atlas mode
        if vars.geely_atlas_mode then
            log.info("GeelyAtlas", "Using Geely Atlas sequence for mirrorCommand")

            -- Alternate between unlock and lock commands
            if last_mirror_action == "unlock" or last_mirror_action == "none" then
                -- Execute lock command using the configurable duration
                log.info("GeelyAtlas", "Mirror executing lock command")
                PinModule.relayControl("Key1", 1) -- Lock button press
                sys.wait(vars.mirror_duration or GEELY_LOCK_DURATION) -- Use configurable duration or default
                PinModule.relayControl("Key1", 0) -- Release lock button
                last_mirror_action = "lock"
            else
                -- Execute unlock command for fixed 5 seconds
                log.info("GeelyAtlas", "Mirror executing unlock command")
                PinModule.relayControl("Key2", 1) -- Unlock button press
                sys.wait(GEELY_UNLOCK_DURATION) -- Fixed 5 seconds for unlock
                PinModule.relayControl("Key2", 0) -- Release unlock button
                last_mirror_action = "unlock"
            end

            log.info("GeelyAtlas", "Mirror function completed with action: " .. last_mirror_action)
        else
            -- Original mirror command logic for other vehicles
            PinModule.relayControl("Go2", 1)
            PinModule.relayControl("Relay3", 1)
            log.info("RELAY3", "ON")
            vars.relay3_state = 1

            -- Wait for fixed duration (not using the configurable mirror_duration)
            sys.wait(3000) -- Fixed 3 seconds for non-Geely mode

            -- Turn off Relay3
            PinModule.relayControl("Go2", 0)
            PinModule.relayControl("Relay3", 0)
            vars.relay3_state = 0
        end
    end)
end

function commands.asCommand()
    -- Initialize key for immobilizer recognition
    local success = pcall(pmd.ldoset, 7, 5) -- Direct pcall to avoid yield issues
    if not success then
        log.error("commands", "Error in pmd.ldoset during asCommand")
        return
    end
    success = pcall(PinModule.relayControl, "Go3", 1) -- Direct pcall
    if not success then
        log.error("commands", "Error in PinModule.relayControl during Go3")
        return
    end

    -- Check if the device is licensed
    if not vars.isLicensed then
        log.warn("License", "Device not licensed. Cannot execute asCommand")
        MP3_Play("/lua/license.mp3") -- Play error sound
        -- Send MQTT notification about license issue
        sys.taskInit(function()
            local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"license_required"}', 0)
            if not success then
                log.error("usermqtt", "Failed to send license notification: " .. (err or "Unknown error"))
            end
        end)
        return
    end

    -- Ensure all timing variables have default values if they're nil
    if not vars.lock_init_duration then vars.lock_init_duration = 2000 end
    if not vars.lock_press_duration then vars.lock_press_duration = 1000 end
    if not vars.lock_wait_duration then vars.lock_wait_duration = 2000 end
    if not vars.between_press_duration then vars.between_press_duration = 1000 end
    if not vars.remote_start_duration then vars.remote_start_duration = 4000 end
    if not vars.relay1_on_duration then vars.relay1_on_duration = 3000 end
    if not vars.relay2_on_duration then vars.relay2_on_duration = 3000 end

    -- Move yielding part into a coroutine-safe context
    sys.taskInit(function()
        -- Check if we're in Geely Atlas mode
        if vars.geely_atlas_mode then
            log.info("VehicleMode", "Using Geely Atlas sequence for asCommand")
            sys.wait(vars.lock_init_duration)  -- wait some for key init
            log.info("GeelyAtlas", "Remote start button press")
            MP3_Play("/lua/as.mp3") -- Play sound 
            -- First lock sequence
            log.info("GeelyAtlas", "First lock press")
            success = pcall(PinModule.relayControl, "Key1", 1)
            if not success then
                log.error("commands", "Error in Key1 control (ON)")
                return
            end

            sys.wait(vars.lock_press_duration)  -- Use configurable duration

            success = pcall(PinModule.relayControl, "Key1", 0)
            if not success then
                log.error("commands", "Error in Key1 control (OFF)")
                return
            end

            sys.wait(vars.between_press_duration)  -- Use configurable duration

            -- Second lock sequence
            log.info("GeelyAtlas", "Second lock press")
            success = pcall(PinModule.relayControl, "Key1", 1)
            if not success then
                log.error("commands", "Error in Key1 control (ON)")
                return
            end

            sys.wait(vars.lock_press_duration)  -- Use configurable duration

            success = pcall(PinModule.relayControl, "Key1", 0)
            if not success then
                log.error("commands", "Error in Key1 control (OFF)")
                return
            end

            sys.wait(vars.between_press_duration)  -- Use configurable duration

            -- Remote start sequence using Relay2
            success = pcall(PinModule.relayControl, "Relay2", 1)
            if not success then
                log.error("commands", "Error in Relay2 control (ON)")
                return
            end

            vars.relay2_state = 1
            log.info("RELAY2", "ON")

            sys.wait(vars.remote_start_duration)  -- Use configurable duration

            success = pcall(PinModule.relayControl, "Relay2", 0)
            if not success then
                log.error("commands", "Error in Relay2 control (OFF)")
                return
            end

            vars.relay2_state = 0
            log.info("RELAY2", "OFF")  -- Fixed log message (was RELAY1)

            vars.as_wait_s2_falling = true
        else
            -- Standard sequence for other vehicles
            MP3_Play("/lua/as.mp3") -- Play sound
            sys.wait(vars.lock_press_duration)  -- Use configurable duration
            sys.wait(vars.lock_wait_duration) -- Wait for configurable duration before proceeding
            vars.relay1_state = 1
            PinModule.relayControl("Relay1", 1) -- Turn on Relay 1
            log.info("RELAY1", "ON")
            sys.wait(vars.relay1_on_duration) -- Wait for configurable duration
            vars.relay2_state = 1
            PinModule.relayControl("Relay2", 1) -- Turn on Relay 2
            log.info("RELAY2", "ON")
            sys.wait(vars.relay2_on_duration) -- Wait for configurable duration
            vars.relay2_state = 0
            PinModule.relayControl("Relay2", 0) -- Turn off Relay 2
            vars.relay1_state = 0
            PinModule.relayControl("Relay1", 0) -- Turn off Relay 1
            vars.as_wait_s2_falling = true

            if vars.key_state == false then
                pmd.ldoset(0, 5) -- Turn off power management
                PinModule.relayControl("Go3", 0) -- Turn off Go3 relay
            end
        end

        -- Common code for both modes
        -- Insert MQTT message
        local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
        if not msg_success then
            log.error("usermqtt", "Failed to insert MQTT message")
        end

        -- Set up auto-shutdown timer if enabled
        if vars.auto_shutdown_enabled then
            -- Record the time of the 'as' command
            vars.last_as_command_time = os.time()

            -- Cancel any existing auto-shutdown timer
            if vars.auto_shutdown_timer_id then
                sys.timerStop(vars.auto_shutdown_timer_id)
                vars.auto_shutdown_timer_id = nil
            end

            -- Set up a new auto-shutdown timer with the configurable duration
            vars.auto_shutdown_timer_id = sys.timerStart(function()
                -- Use pcall to catch any errors in the auto-shutdown function
                local success, err = pcall(function()
                    -- Check if the car is still running by checking voltage
                    if not SensorModule or type(SensorModule) ~= "table" or not SensorModule.readVoltage then
                        log.error("AutoShutdown", "SensorModule not available or readVoltage function missing")
                        return
                    end

                    local voltage = SensorModule.readVoltage()
                    voltage = tonumber(voltage) or 0

                    log.info("AutoShutdown", "Auto-shutdown check - Current voltage: " .. voltage)

                    -- Only shut down if voltage indicates the car is still running
                    if voltage >= 13.5 then
                        log.info("AutoShutdown", string.format("Auto-shutdown triggered after %d minutes. Current voltage: %s",
                            vars.auto_shutdown_minutes, voltage))

                        -- Execute the untarCommand
                        local untar_success, untar_err = pcall(commands.untarCommand, 2000)
                        if not untar_success then
                            log.error("AutoShutdown", "Failed to execute untar command: " .. (untar_err or "Unknown error"))
                        end

                        -- Send MQTT notification
                        local mqtt_success, mqtt_err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                            string.format('{"status":"Auto-shutdown executed after %d minutes of inactivity"}', vars.auto_shutdown_minutes), 0)
                        if not mqtt_success then
                            log.error("usermqtt", "Failed to send auto-shutdown notification: " .. (mqtt_err or "Unknown error"))
                        end
                    else
                        log.info("AutoShutdown", "Auto-shutdown canceled - car appears to be off already. Voltage: " .. voltage)
                    end
                end)

                if not success then
                    log.error("AutoShutdown", "Error in auto-shutdown timer: " .. (err or "Unknown error"))
                end

                -- Clear the timer ID
                vars.auto_shutdown_timer_id = nil
            end, vars.auto_shutdown_time)

            log.info("AutoShutdown", string.format("Auto-shutdown timer set for %d minutes", vars.auto_shutdown_minutes))
        end
    end)
end
function commands.testCommand()
    -- Non-yielding part
    pmd.ldoset(7, 5) -- Set the power management directly
    PinModule.relayControl("Go2", 1) -- Control relay directly
    PinModule.relayControl("Go3", 1) -- Control relay directly
    -- Move yielding part into a coroutine-safe context
    sys.taskInit(function()
        sys.wait(1000) -- Wait for 1 second before proceeding
        MP3_Play("/lua/as.mp3") -- Play sound
        vars.relay1_state = 1
        PinModule.relayControl("Relay1", 1) -- Turn on Relay 1
        PinModule.relayControl("Relay3", 1) -- Turn on Relay 1

        log.info("RELAY1", "ON")
        sys.wait(2000) -- Wait for 4 seconds
        vars.relay2_state = 1
        PinModule.relayControl("Relay2", 1) -- Turn on Relay 2
        log.info("RELAY2", "ON")
        sys.wait(2000) -- Wait for 3 seconds
        vars.relay2_state = 0
        PinModule.relayControl("Relay2", 0) -- Turn off Relay 2
        vars.relay1_state = 0
        PinModule.relayControl("Relay1", 0) -- Turn off Relay 1
        PinModule.relayControl("Relay3", 1) -- Turn on Relay 3

        vars.as_wait_s2_falling = true

        if vars.key_state == false then
            pmd.ldoset(0, 5) -- Turn off power management
            PinModule.relayControl("Go2", 0) -- Turn off Go2 relay
            PinModule.relayControl("Go3", 0) -- Turn off Go3 relay
        end

        -- Insert MQTT message
        local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
        if not msg_success then
            log.error("usermqtt", "Failed to insert MQTT message")
        end
    end)
end

function commands.asaCommand(delay_ms)
    -- Initialize key for immobilizer recognition
    local success = pcall(pmd.ldoset, 7, 5) -- Direct pcall to avoid yield issues
    if not success then
        log.error("commands", "Error in pmd.ldoset during asaCommand")
        return
    end
    success = pcall(PinModule.relayControl, "Go3", 1) -- Direct pcall
    if not success then
        log.error("commands", "Error in PinModule.relayControl during Go3")
        return
    end

    vars.relay1_state = 1
    PinModule.relayControl("Relay1", 1) -- Direct call since this may yield
    log.info("RELAY1", "ON")

    sys.taskInit(function()
        sys.wait(3000)

        pmd.ldoset(7, 5) -- Direct call since this may yield

        vars.relay2_state = 1
        PinModule.relayControl("Relay2", 1) -- Direct call since this may yield
        PinModule.relayControl("Relay3", 1) -- Direct call since this may yield

        log.info("RELAY2", "ON")
        sys.wait(delay_ms)

        PinModule.relayControl("Relay2", 0) -- Direct call since this may yield
        PinModule.relayControl("Relay3", 0) -- Direct call since this may yield

        log.info("RELAY2", "OFF")
        sys.wait(1000)

        pmd.ldoset(0, 5) -- Direct call since this may yield

        MP3_Play("/lua/as.mp3")

        -- Insert MQTT message
        local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
        if not msg_success then
            log.error("usermqtt", "Failed to insert MQTT message")
        end
    end)
end

function commands.untarCommand(duration)
    -- Check if the device is licensed
    if not vars.isLicensed then
        log.warn("License", "Device not licensed. Cannot execute untarCommand")
        MP3_Play("/lua/license.mp3") -- Play error sound
        -- Send MQTT notification about license issue
        sys.taskInit(function()
            local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"license_required"}', 0)
            if not success then
                log.error("usermqtt", "Failed to send license notification: " .. (err or "Unknown error"))
            end
        end)
        return
    end

    -- Cancel any existing auto-shutdown timer
    if vars.auto_shutdown_timer_id then
        log.info("AutoShutdown", "Canceling auto-shutdown timer due to manual untar command")
        sys.timerStop(vars.auto_shutdown_timer_id)
        vars.auto_shutdown_timer_id = nil
    end

    -- Initialize key for immobilizer recognition (for both modes)
    local success = pcall(pmd.ldoset, 7, 5) -- Direct pcall to avoid yield issues
    if not success then
        log.error("commands", "Error in pmd.ldoset during untarCommand")
        return
    end
    success = pcall(PinModule.relayControl, "Go3", 1) -- Direct pcall
    if not success then
        log.error("commands", "Error in PinModule.relayControl during Go3")
        return
    end

    -- Check if we're in Geely Atlas mode
    if vars.geely_atlas_mode then
        log.info("VehicleMode", "Using Geely Atlas sequence for untarCommand")

        sys.taskInit(function()
            sys.wait(2000)  -- Hold for 3 seconds to ensure engine stops
            MP3_Play("/lua/untar.mp3")
            vars.relay2_state = 1
            PinModule.relayControl("Relay2", 1)  -- Direct call since this may yield
            sys.wait(1000)  -- Hold for 3 seconds to ensure engine stops
            vars.relay2_state = 0
            PinModule.relayControl("Relay2", 0)  -- Direct call since this may yield
            sys.wait(1000)  -- Hold for 3 seconds to ensure engine stops
            vars.relay2_state = 1
            PinModule.relayControl("Relay2", 1)  -- Direct call since this may yield
            sys.wait(1000)  -- Hold for 3 seconds to ensure engine stops
            vars.relay2_state = 0
            PinModule.relayControl("Relay2", 0)  -- Direct call since this may yield
            sys.wait(2000)  -- Hold for 3 seconds to ensure engine stops
            -- Power down if needed
            if vars.key_state == false then
                pmd.ldoset(0, 5)
            end
        end)
    else
        -- Standard sequence for other vehicles
        MP3_Play("/lua/untar.mp3")
        vars.relay2_state = 1
        PinModule.relayControl("Relay2", 1)  -- Direct call since this may yield

        sys.taskInit(function()
            sys.wait(duration)

            vars.relay2_state = 0
            PinModule.relayControl("Relay2", 0)  -- Direct call since this may yield
        end)
    end
end

function commands.handleSetMqtt(server_key)
    -- Attempt to set the MQTT server using the usermqtt module
    local success, err = pcall(usermqtt.setMqttServer, server_key)

    if not success then
        -- Log the error and prepare the error message
        log.error("usermqtt", "Failed to set MQTT server: " .. (err or "Unknown error"))
        sms.send(vars.callback_number, "Error: Failed to set MQTT server.")
        return false, "Error: Failed to set MQTT server."
    else
        -- Send a confirmation SMS to the sender
        sms.send(vars.callback_number, "MQTT server set to " .. server_key .. ". Reconnecting...")
        -- Log the successful server set
        log.info("usermqtt", "MQTT server set to " .. server_key .. " successfully.")
        local r = 1 -- Use the appropriate restart argument based on your platform
        local success, err = pcall(function()
            sys.restart(r)
        end)
        if not success then
            log.error("commands", "failed to reboot: " .. (err or "Unknown error"))
        end

        return true, "MQTT server set to " .. server_key .. ". Reconnecting..."
    end
end

-- Function to set timing parameters remotely
function commands.setTimingParameter(param_name, value_ms)
    -- Validate input
    if not param_name or not value_ms then
        return false, "Missing parameter name or value"
    end

    -- Convert to number if it's a string
    if type(value_ms) == "string" then
        value_ms = tonumber(value_ms)
    end

    -- Validate value is a number
    if not value_ms or type(value_ms) ~= "number" then
        return false, "Invalid timing value: must be a number"
    end

    -- Set minimum and maximum limits
    local min_ms = 100   -- 0.1 seconds minimum
    local max_ms = 10000 -- 10 seconds maximum

    if value_ms < min_ms then value_ms = min_ms end
    if value_ms > max_ms then value_ms = max_ms end

    -- Set the appropriate variable
    if param_name == "mirror" then
        vars.mirror_duration = value_ms
        log.info("Timing", "Mirror duration set to " .. value_ms .. "ms (for Geely Atlas lock action)")
    elseif param_name == "lock_press" then
        vars.lock_press_duration = value_ms
        log.info("Timing", "Lock press duration set to " .. value_ms .. "ms")
    elseif param_name == "unlock_press" then
        vars.unlock_press_duration = value_ms
        log.info("Timing", "Unlock press duration set to " .. value_ms .. "ms")
    elseif param_name == "lock_wait" then
        vars.lock_wait_duration = value_ms
        log.info("Timing", "Lock wait duration set to " .. value_ms .. "ms")
    elseif param_name == "unlock_wait" then
        vars.unlock_wait_duration = value_ms
        log.info("Timing", "Unlock wait duration set to " .. value_ms .. "ms")
    elseif param_name == "between_press" then
        vars.between_press_duration = value_ms
        log.info("Timing", "Between press duration set to " .. value_ms .. "ms")
    elseif param_name == "remote_start" then
        vars.remote_start_duration = value_ms
        log.info("Timing", "Remote start duration set to " .. value_ms .. "ms")
    elseif param_name == "relay1_on" then
        vars.relay1_on_duration = value_ms
        log.info("Timing", "Relay1 on duration set to " .. value_ms .. "ms")
    elseif param_name == "relay2_on" then
        vars.relay2_on_duration = value_ms
        log.info("Timing", "Relay2 on duration set to " .. value_ms .. "ms")
    elseif param_name == "lock_init" then
        vars.lock_init_duration = value_ms
        log.info("Timing", "Lock initialization duration set to " .. value_ms .. "ms")
    else
        return false, "Unknown parameter name: " .. param_name
    end

    -- Save the settings to persistent storage
    local success, err = pcall(my_utils.writeToFile, "/user_dir/" .. param_name .. "_duration.txt", tostring(value_ms))
    if not success then
        log.error("FileUtils", "Failed to save timing parameter: " .. (err or "Unknown error"))
        return false, "Failed to save setting"
    end

    return true, "Timing parameter set successfully"
end

-- Function to get all timing parameters
function commands.getTimingParameters()
    local params = {
        lock_press = vars.lock_press_duration,
        unlock_press = vars.unlock_press_duration,
        lock_wait = vars.lock_wait_duration,
        unlock_wait = vars.unlock_wait_duration,
        between_press = vars.between_press_duration,
        remote_start = vars.remote_start_duration,
        mirror = vars.mirror_duration,
        relay1_on = vars.relay1_on_duration,
        relay2_on = vars.relay2_on_duration,
        lock_init = vars.lock_init_duration
    }

    return params
end

-- Function to parse time units and convert to seconds
local function parseTimeUnit(timeStr)
    if not timeStr or timeStr == "" then
        return nil, "Empty time string"
    end

    -- Convert to lowercase for easier matching
    local lowerStr = string.lower(timeStr)

    -- Extract number and unit
    local number, unit = string.match(lowerStr, "^(%d+)%s*(%a*)$")

    if not number then
        return nil, "Invalid time format"
    end

    number = tonumber(number)
    if not number or number <= 0 then
        return nil, "Invalid number"
    end

    -- Default to seconds if no unit specified
    if not unit or unit == "" or unit == "s" or unit == "sec" or unit == "second" or unit == "seconds" then
        return number, nil
    elseif unit == "m" or unit == "min" or unit == "minut" or unit == "minute" or unit == "minutes" then
        return number * 60, nil
    elseif unit == "h" or unit == "hr" or unit == "hour" or unit == "hours" then
        return number * 3600, nil
    else
        return nil, "Unknown time unit: " .. unit
    end
end

-- Function to enable GPS tracking auto-publish
function commands.trackOnCommand()
    log.info("Tracking", "Enabling GPS tracking auto-publish")

    -- Stop any existing tracking timer
    if vars.tracking_timer_id then
        sys.timerStop(vars.tracking_timer_id)
        vars.tracking_timer_id = nil
    end

    -- Enable tracking
    vars.tracking_enabled = true

    -- Start the tracking timer with current interval
    vars.tracking_timer_id = sys.timerLoopStart(function()
        -- Check voltage before triggering upload
        local current_voltage = tonumber(SensorModule.readVoltage()) or 0
        if current_voltage >= vars.tracking_voltage_threshold then
            -- Check speed filter if minimum speed is set
            if vars.tracking_min_speed > 0 then
                local lat, lng, current_speed = SensorModule.readGPS()
                current_speed = tonumber(current_speed) or 0
                if current_speed >= vars.tracking_min_speed then
                    log.info("Tracking", string.format("Auto-publish triggered (Voltage: %.2fV, Speed: %.1f km/h)", current_voltage, current_speed))
                    vars.upload_flag = true
                else
                    log.info("Tracking", string.format("Auto-publish skipped - speed too low (%.1f < %d km/h)",
                        current_speed, vars.tracking_min_speed))
                end
            else
                -- No speed filtering, just publish
                log.info("Tracking", string.format("Auto-publish triggered by tracking timer (Voltage: %.2fV)", current_voltage))
                vars.upload_flag = true
            end
        else
            log.info("Tracking", string.format("Auto-publish skipped - voltage too low (%.2fV < %.2fV)",
                current_voltage, vars.tracking_voltage_threshold))
        end
    end, vars.tracking_interval_ms)

    -- Save state to persistent storage
    local success, err = pcall(my_utils.writeToFile, "/user_dir/tracking_enabled.txt", "true")
    if not success then
        log.error("Tracking", "Failed to save tracking state: " .. (err or "Unknown error"))
    end

    -- Send confirmation via MQTT with voltage status
    local current_voltage = tonumber(SensorModule.readVoltage()) or 0
    local car_running = current_voltage >= vars.tracking_voltage_threshold
    local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
        string.format('{"status":"tracking_enabled","interval":%d,"voltage":%.2f,"car_running":%s,"voltage_threshold":%.1f}',
        vars.tracking_interval, current_voltage, tostring(car_running), vars.tracking_voltage_threshold), 0)
    if not msg_success then
        log.error("Tracking", "Failed to send tracking enabled confirmation")
    end

    log.info("Tracking", string.format("GPS tracking enabled with %d second interval", vars.tracking_interval))
end

-- Function to disable GPS tracking auto-publish
function commands.trackOffCommand()
    log.info("Tracking", "Disabling GPS tracking auto-publish")

    -- Stop tracking timer
    if vars.tracking_timer_id then
        sys.timerStop(vars.tracking_timer_id)
        vars.tracking_timer_id = nil
    end

    -- Disable tracking
    vars.tracking_enabled = false

    -- Save state to persistent storage
    local success, err = pcall(my_utils.writeToFile, "/user_dir/tracking_enabled.txt", "false")
    if not success then
        log.error("Tracking", "Failed to save tracking state: " .. (err or "Unknown error"))
    end

    -- Send confirmation via MQTT with voltage status
    local current_voltage = tonumber(SensorModule.readVoltage()) or 0
    local car_running = current_voltage >= vars.tracking_voltage_threshold
    local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
        string.format('{"status":"tracking_disabled","voltage":%.2f,"car_running":%s,"voltage_threshold":%.1f}',
        current_voltage, tostring(car_running), vars.tracking_voltage_threshold), 0)
    if not msg_success then
        log.error("Tracking", "Failed to send tracking disabled confirmation")
    end

    log.info("Tracking", "GPS tracking disabled")
end

-- Function to set GPS tracking timer interval
function commands.trackTimerCommand(timeStr)
    log.info("Tracking", "Setting tracking timer interval: " .. (timeStr or "nil"))

    if not timeStr or timeStr == "" then
        log.error("Tracking", "No time interval specified")
        local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
            '{"status":"error","message":"No time interval specified"}', 0)
        return false, "No time interval specified"
    end

    -- Parse the time string
    local seconds, err = parseTimeUnit(timeStr)
    if not seconds then
        log.error("Tracking", "Invalid time format: " .. err)
        local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
            string.format('{"status":"error","message":"Invalid time format: %s"}', err), 0)
        return false, "Invalid time format: " .. err
    end

    -- Validate reasonable limits (1 second to 24 hours)
    if seconds < 1 or seconds > 86400 then
        log.error("Tracking", "Time interval out of range (1s - 24h)")
        local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
            '{"status":"error","message":"Time interval out of range (1s - 24h)"}', 0)
        return false, "Time interval out of range (1s - 24h)"
    end

    -- Update tracking interval
    vars.tracking_interval = seconds
    vars.tracking_interval_ms = seconds * 1000

    -- Save interval to persistent storage
    local success, err = pcall(my_utils.writeToFile, "/user_dir/tracking_interval.txt", tostring(seconds))
    if not success then
        log.error("Tracking", "Failed to save tracking interval: " .. (err or "Unknown error"))
    end

    -- If tracking is currently enabled, restart the timer with new interval
    if vars.tracking_enabled and vars.tracking_timer_id then
        sys.timerStop(vars.tracking_timer_id)
        vars.tracking_timer_id = sys.timerLoopStart(function()
            -- Check voltage before triggering upload
            local current_voltage = tonumber(SensorModule.readVoltage()) or 0
            if current_voltage >= vars.tracking_voltage_threshold then
                -- Check speed filter if minimum speed is set
                if vars.tracking_min_speed > 0 then
                    local lat, lng, current_speed = SensorModule.readGPS()
                    current_speed = tonumber(current_speed) or 0
                    if current_speed >= vars.tracking_min_speed then
                        log.info("Tracking", string.format("Auto-publish triggered (Voltage: %.2fV, Speed: %.1f km/h)", current_voltage, current_speed))
                        vars.upload_flag = true
                    else
                        log.info("Tracking", string.format("Auto-publish skipped - speed too low (%.1f < %d km/h)",
                            current_speed, vars.tracking_min_speed))
                    end
                else
                    -- No speed filtering, just publish
                    log.info("Tracking", string.format("Auto-publish triggered by tracking timer (Voltage: %.2fV)", current_voltage))
                    vars.upload_flag = true
                end
            else
                log.info("Tracking", string.format("Auto-publish skipped - voltage too low (%.2fV < %.2fV)",
                    current_voltage, vars.tracking_voltage_threshold))
            end
        end, vars.tracking_interval_ms)
    end

    -- Send confirmation via MQTT with voltage status
    local current_voltage = tonumber(SensorModule.readVoltage()) or 0
    local car_running = current_voltage >= vars.tracking_voltage_threshold
    local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
        string.format('{"status":"tracking_timer_set","interval":%d,"enabled":%s,"voltage":%.2f,"car_running":%s,"voltage_threshold":%.1f}',
        vars.tracking_interval, tostring(vars.tracking_enabled), current_voltage, tostring(car_running), vars.tracking_voltage_threshold), 0)
    if not msg_success then
        log.error("Tracking", "Failed to send timer confirmation")
    end

    log.info("Tracking", string.format("Tracking timer interval set to %d seconds", vars.tracking_interval))
    return true, "Timer interval set successfully"
end

-- Function to set GPS tracking minimum speed filter
function commands.trackSpeedCommand(speedStr)
    log.info("Tracking", "Setting tracking minimum speed: " .. (speedStr or "nil"))

    if not speedStr or speedStr == "" then
        log.error("Tracking", "No speed value specified")
        local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
            '{"status":"error","message":"No speed value specified"}', 0)
        return false, "No speed value specified"
    end

    -- Parse the speed string
    local speed = tonumber(speedStr)
    if not speed then
        log.error("Tracking", "Invalid speed format: " .. speedStr)
        local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
            string.format('{"status":"error","message":"Invalid speed format: %s"}', speedStr), 0)
        return false, "Invalid speed format: " .. speedStr
    end

    -- Validate reasonable limits (0 to 200 km/h)
    if speed < 0 or speed > 200 then
        log.error("Tracking", "Speed value out of range (0-200 km/h)")
        local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
            '{"status":"error","message":"Speed value out of range (0-200 km/h)"}', 0)
        return false, "Speed value out of range (0-200 km/h)"
    end

    -- Update tracking minimum speed
    vars.tracking_min_speed = speed

    -- Save speed filter to persistent storage
    local success, err = pcall(my_utils.writeToFile, "/user_dir/tracking_min_speed.txt", tostring(speed))
    if not success then
        log.error("Tracking", "Failed to save tracking minimum speed: " .. (err or "Unknown error"))
    end

    -- Send confirmation via MQTT
    local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg",
        string.format('{"status":"GPS tracking minimum speed set to %d km/h"}', vars.tracking_min_speed), 0)
    if not msg_success then
        log.error("Tracking", "Failed to send speed filter confirmation")
    end

    log.info("Tracking", string.format("Tracking minimum speed set to %d km/h", vars.tracking_min_speed))
    return true, "Speed filter set successfully"
end

return commands
