-- test_tracking.lua
-- Simple test script to validate the GPS tracking functionality

-- Mock dependencies for testing
local log = {
    info = function(tag, ...) print("[INFO]", tag, ...) end,
    error = function(tag, ...) print("[ERROR]", tag, ...) end,
    warn = function(tag, ...) print("[WARN]", tag, ...) end
}

local sys = {
    timerLoopStart = function(func, interval)
        print("[TIMER] Started timer with interval:", interval, "ms")
        return "timer_id_" .. math.random(1000, 9999)
    end,
    timerStop = function(timer_id)
        print("[TIMER] Stopped timer:", timer_id)
    end
}

local usermqtt = {
    insertMsg = function(topic, payload, qos)
        print("[MQTT] Sending message:", topic, payload, qos)
        return true
    end,
    clientID = function()
        return "test_device_123"
    end
}

local my_utils = {
    writeToFile = function(path, content)
        print("[FILE] Writing to", path, ":", content)
        return true
    end
}

-- Mock SensorModule for testing
local SensorModule = {
    readVoltage = function()
        -- Simulate different voltage levels for testing
        return math.random(110, 150) / 10  -- Random voltage between 11.0V and 15.0V
    end
}

-- Mock variables
local vars = {
    tracking_enabled = false,
    tracking_timer_id = nil,
    tracking_interval = 300,
    tracking_interval_ms = 300000,
    tracking_voltage_threshold = 13.5,
    upload_flag = false
}

-- Load the commands module functions (simplified for testing)
local function parseTimeUnit(timeStr)
    if not timeStr or timeStr == "" then
        return nil, "Empty time string"
    end
    
    local lowerStr = string.lower(timeStr)
    local number, unit = string.match(lowerStr, "^(%d+)%s*(%a*)$")
    
    if not number then
        return nil, "Invalid time format"
    end
    
    number = tonumber(number)
    if not number or number <= 0 then
        return nil, "Invalid number"
    end
    
    if not unit or unit == "" or unit == "s" or unit == "sec" or unit == "second" or unit == "seconds" then
        return number, nil
    elseif unit == "m" or unit == "min" or unit == "minut" or unit == "minute" or unit == "minutes" then
        return number * 60, nil
    elseif unit == "h" or unit == "hr" or unit == "hour" or unit == "hours" then
        return number * 3600, nil
    else
        return nil, "Unknown time unit: " .. unit
    end
end

local function trackOnCommand()
    log.info("Tracking", "Enabling GPS tracking auto-publish")
    
    if vars.tracking_timer_id then
        sys.timerStop(vars.tracking_timer_id)
        vars.tracking_timer_id = nil
    end
    
    vars.tracking_enabled = true
    
    vars.tracking_timer_id = sys.timerLoopStart(function()
        -- Check voltage before triggering upload
        local current_voltage = tonumber(SensorModule.readVoltage()) or 0
        if current_voltage >= vars.tracking_voltage_threshold then
            log.info("Tracking", string.format("Auto-publish triggered by tracking timer (Voltage: %.2fV)", current_voltage))
            vars.upload_flag = true
        else
            log.info("Tracking", string.format("Auto-publish skipped - voltage too low (%.2fV < %.2fV)",
                current_voltage, vars.tracking_voltage_threshold))
        end
    end, vars.tracking_interval_ms)
    
    my_utils.writeToFile("/user_dir/tracking_enabled.txt", "true")
    
    local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg", 
        string.format('{"status":"tracking_enabled","interval":%d}', vars.tracking_interval), 0)
    
    log.info("Tracking", string.format("GPS tracking enabled with %d second interval", vars.tracking_interval))
end

local function trackOffCommand()
    log.info("Tracking", "Disabling GPS tracking auto-publish")
    
    if vars.tracking_timer_id then
        sys.timerStop(vars.tracking_timer_id)
        vars.tracking_timer_id = nil
    end
    
    vars.tracking_enabled = false
    
    my_utils.writeToFile("/user_dir/tracking_enabled.txt", "false")
    
    local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg", 
        '{"status":"tracking_disabled"}', 0)
    
    log.info("Tracking", "GPS tracking disabled")
end

local function trackTimerCommand(timeStr)
    log.info("Tracking", "Setting tracking timer interval: " .. (timeStr or "nil"))
    
    if not timeStr or timeStr == "" then
        log.error("Tracking", "No time interval specified")
        return false, "No time interval specified"
    end
    
    local seconds, err = parseTimeUnit(timeStr)
    if not seconds then
        log.error("Tracking", "Invalid time format: " .. err)
        return false, "Invalid time format: " .. err
    end
    
    if seconds < 1 or seconds > 86400 then
        log.error("Tracking", "Time interval out of range (1s - 24h)")
        return false, "Time interval out of range (1s - 24h)"
    end
    
    vars.tracking_interval = seconds
    vars.tracking_interval_ms = seconds * 1000
    
    my_utils.writeToFile("/user_dir/tracking_interval.txt", tostring(seconds))
    
    if vars.tracking_enabled and vars.tracking_timer_id then
        sys.timerStop(vars.tracking_timer_id)
        vars.tracking_timer_id = sys.timerLoopStart(function()
            log.info("Tracking", "Auto-publish triggered by tracking timer")
            vars.upload_flag = true
        end, vars.tracking_interval_ms)
    end
    
    local msg_success = usermqtt.insertMsg(usermqtt.clientID() .. "/msg", 
        string.format('{"status":"tracking_timer_set","interval":%d,"enabled":%s}', 
        vars.tracking_interval, tostring(vars.tracking_enabled)), 0)
    
    log.info("Tracking", string.format("Tracking timer interval set to %d seconds", vars.tracking_interval))
    return true, "Timer interval set successfully"
end

-- Test functions
local function runTests()
    print("=== GPS Tracking Functionality Tests ===\n")
    
    -- Test 1: Time unit parsing
    print("Test 1: Time unit parsing")
    local testCases = {
        {"300", 300},
        {"5minut", 300},
        {"5 minutes", 300},
        {"1hour", 3600},
        {"2h", 7200},
        {"invalid", nil},
        {"", nil}
    }
    
    for _, case in ipairs(testCases) do
        local input, expected = case[1], case[2]
        local result, err = parseTimeUnit(input)
        if expected then
            if result == expected then
                print("  ✓ '" .. input .. "' -> " .. result .. " seconds")
            else
                print("  ✗ '" .. input .. "' -> expected " .. expected .. ", got " .. (result or "nil"))
            end
        else
            if not result then
                print("  ✓ '" .. input .. "' -> error: " .. (err or "unknown"))
            else
                print("  ✗ '" .. input .. "' -> expected error, got " .. result)
            end
        end
    end
    
    print("\nTest 2: Track On Command")
    trackOnCommand()
    print("  Tracking enabled:", vars.tracking_enabled)
    print("  Timer ID:", vars.tracking_timer_id)
    
    print("\nTest 3: Track Timer Command")
    local success, msg = trackTimerCommand("1minut")
    print("  Success:", success, "Message:", msg)
    print("  New interval:", vars.tracking_interval, "seconds")
    
    print("\nTest 4: Track Off Command")
    trackOffCommand()
    print("  Tracking enabled:", vars.tracking_enabled)
    print("  Timer ID:", vars.tracking_timer_id)
    
    print("\nTest 5: Invalid Timer Command")
    local success, msg = trackTimerCommand("invalid")
    print("  Success:", success, "Message:", msg)
    
    print("\n=== All Tests Completed ===")
end

-- Run the tests
runTests()
