-- variables.lua
-- This module contains variables that can be shared across different files

local variables = {}

-- Declare your variables here
variables.relay1_state = 0
variables.relay2_state = 0
variables.relay3_state = 0
variables.go2_state = 0
variables.go3_state = 0
variables.ble_adv_status = 0
variables.upload_flag = false
variables.upload_switch = true
variables.phone_number1 = nil
variables.phone_number2 = nil
variables.phone_number3 = nil
variables.phone_number4 = 1411
variables.sms_data = nil
variables.mqtt_data = nil
variables.callback_number = nil
variables.alarm_state = false
variables.key_state = false
variables.asa_state = false
variables.as_wait_s2_falling = true
variables.timers_queue = {}
variables.currentTime = 0
variables.sound_flag = true
variables.gps_flag = true
variables.commandCounter = {}
variables.triggerCount = 3
variables.device_name = "aslaa"  -- Set the device name here
variables.carAlreadyStarted = false
variables.isLicensed = true
variables.voltage_offset = 0  -- Default voltage offset value, will be loaded from /user_dir/volt_offset.txt
variables.voltage_threshold = 0.5  -- Default threshold, will be loaded from /user_dir/volt_threshold.txt
variables.voltage_notify_flag = false  -- Default notify flag, will be loaded from /user_dir/volt_notify.txt

-- Auto-shutdown feature settings
variables.auto_shutdown_enabled = true  -- Enable auto-shutdown feature by default
variables.auto_shutdown_timer_id = nil  -- Timer ID for auto-shutdown
variables.auto_shutdown_minutes = 30  -- Default: 30 minutes (user-configurable)
variables.auto_shutdown_time = variables.auto_shutdown_minutes * 60 * 1000  -- Convert minutes to milliseconds
variables.last_as_command_time = 0  -- Timestamp of the last 'as' command

-- Vehicle-specific settings
variables.geely_atlas_mode = false  -- Special mode for Geely Atlas vehicles

-- GPS warning settings
variables.gps_warned = false  -- Flag to track if we've already warned about GPS unavailability
variables.last_gps_status = false  -- Track the last known GPS status

-- Logging and diagnostics settings
variables.log_level = log.LOGLEVEL_INFO  -- Default log level
variables.diagnostics_enabled = true  -- Enable collection of diagnostic data
variables.log_to_file = true  -- Enable logging to file

-- Command timing settings (all values in milliseconds)
variables.lock_init_duration = 2000      -- Duration to wait for key initialization
variables.lock_press_duration = 1000      -- Duration to hold lock button
variables.unlock_press_duration = 1000    -- Duration to hold unlock button
variables.lock_wait_duration = 2000       -- Wait before pressing lock button
variables.unlock_wait_duration = 1000     -- Wait before pressing unlock button
variables.between_press_duration = 1000   -- Duration between multiple button presses
variables.remote_start_duration = 4000    -- Duration to hold remote start button
variables.mirror_duration = 3000          -- Duration for mirror operation
variables.relay1_on_duration = 3000       -- Duration to keep Relay1 on
variables.relay2_on_duration = 3000       -- Duration to keep Relay2 on

-- GPS Tracking auto-publish settings
variables.tracking_enabled = false        -- Enable/disable auto tracking publish
variables.tracking_timer_id = nil         -- Timer ID for tracking auto-publish
variables.tracking_interval = 300         -- Default tracking interval in seconds (5 minutes)
variables.tracking_interval_ms = variables.tracking_interval * 1000  -- Convert to milliseconds

return variables
